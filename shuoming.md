`1	 
 写乎 (DraftFlux) - 项目最终蓝图
版本: 2.0 (最终版)
状态: 行动纲领 (Master Plan)
目标: 本文档是“写乎”项目的唯一真相来源 (Single Source of Truth)。它整合了项目的最终技术选型（技术蓝图）与从概念到成熟产品的完整分阶段开发计划（全周期执行计划），旨在为个人开发者提供一份清晰、全面、可执行的终极指南。
第一部分：技术蓝图 (The Technical Blueprint)
本部分详细列出了构建“写乎”所需的所有技术、工具和平台。这个技术栈经过精心选择，旨在最大化个人开发者的效率、保证产品的可扩展性，并从第一天起就为 SEO 和性能打下坚实基础。
技术栈总览
类别
主要技术 / 平台
核心框架
Next.js (App Router), React, TypeScript
UI 与样式
Tailwind CSS, shadcn/ui
核心功能库
CodeMirror 6, Dexie.js, isomorphic-git, unified (Remark/Rehype), Shiki
后端与数据
Next.js API Routes, Vercel Postgres / Supabase
开发与运维
GitHub, Vercel, GitHub Actions
分析与监控
Vercel Analytics, Sentry

技术选型详解
核心框架：Next.js
为何选择: Next.js 是我们技术栈的基石。它不仅是一个 React 框架，更是一个集成了 服务器端渲染 (SSR)、静态站点生成 (SSG) 和 后端 API 的全栈解决方案。这对实现产品的公共页面（如落地页、博客）的 SEO 优化 至关重要。
实施:
使用 App Router 进行文件系统路由。
公共页面（落地页）采用 SSG 以获得最佳性能。
需要登录的应用核心（编辑器本身）使用 'use client'; 指令，进行客户端渲染。
未来可能的用户公开分享页面，采用 ISR 或 SSR。
UI 与样式：Tailwind CSS + shadcn/ui
为何选择: 这套组合提供了极致的开发速度和完全的设计自由度。Tailwind CSS 的原子化类让你无需离开 HTML 即可构建界面；shadcn/ui 提供了高质量、无障碍且完全可定制的组件基础。
实施: 通过 pnpm dlx shadcn-ui@latest init 初始化，然后按需添加组件。
核心功能库
CodeMirror 6: 用于构建高性能、高度可定制的 Markdown 编辑器。
Dexie.js: 优雅地封装 IndexedDB，实现强大的**本地优先 (Local-first)**数据存储。
isomorphic-git: 在浏览器中实现 Git 的核心能力，是产品差异化的关键。
unified (Remark/Rehype): 用于 Markdown 到 HTML 的精准转换，是实现高级公众号排版的利器。
Shiki: 用于在服务端渲染出与 VS Code 风格一致的、对 SEO 友好的代码高亮。
后端与数据
Next.js API Routes: 用于处理所有需要后端逻辑的场景，如 GitHub OAuth 认证、Git 远程操作的 CORS 代理、未来的支付处理等。这避免了维护一个独立后端服务的复杂性。
Vercel Postgres / Supabase: 用于存储用户账户、设置、订阅状态等结构化数据。
开发与运维：GitHub + Vercel
为何选择: 这是现代 Web 开发的“黄金搭档”。代码托管在 GitHub，Vercel 与其深度集成，可以实现无缝的 持续集成与部署 (CI/CD)。
实施:
在 Vercel 上关联你的 GitHub 仓库。
之后每一次 git push 都会自动触发构建和部署。
每个 Pull Request 都会自动生成一个可供测试的预览环境。
第二部分：全周期执行计划 (The Full-Lifecycle Execution Plan)
本部分将整个项目分解为五个连续的、可管理的阶段，指导你从概念一步步走向成熟产品。
阶段一：概念与设计 (Phase 1: Concept & Design)
目标: 将产品构想转化为具体的设计蓝图和技术方案，为开发阶段扫清一切障碍。
任务 (Task)
详细步骤 (Detailed Steps)
关键工具 / 技术
D1.1: 界面线框图
1. 手绘或使用 Excalidraw 绘制核心界面的低保真线框图。<br>2. 明确主编辑界面、历史记录面板、设置页面的布局与交互。
Excalidraw, Figma
D1.2: 视觉风格指南
1. 定义产品的主色、辅助色、背景色、文本颜色（区分亮/暗模式）。<br>2. 选定 UI 字体 (Inter) 和代码字体 (Source Code Pro)。<br>3. 确定统一的间距、圆角等设计规范。
Figma, Tailwind Config
D1.3: 项目初始化
1. 使用 create-next-app 初始化项目。<br>2. 集成 TypeScript, Tailwind CSS, ESLint, Prettier。<br>3. 初始化 shadcn/ui。<br>4. 在 GitHub 创建仓库并完成首次提交。
Next.js, Git, pnpm

阶段二：MVP 开发 (Phase 2: MVP Development)
目标: 集中精力构建产品的核心功能，快速开发出一个可用的最小化可行产品。
任务 (Task)
详细步骤 (Detailed Steps)
关键技术
D2.1: 核心编辑器
1. 实现双栏布局：左侧 CodeMirror 编辑器，右侧实时预览。<br>2. 实现编辑器与 React state 的双向绑定（带防抖）。<br>3. 使用 unified 实现基础 Markdown 预览。
React, CodeMirror 6, unified
D2.2: 本地存储
1. 集成 Dexie.js，设计 documents 数据表。<br>2. 实现应用加载时自动读取、内容变化时自动保存的逻辑。
Dexie.js, IndexedDB
D2.3: 基础 Git 工作流
1. 集成 isomorphic-git 和 lightning-fs。<br>2. 实现“保存版本”功能，允许用户输入 commit message 并执行 git commit。<br>3. 实现“历史记录”功能，可以拉取并展示 git log 列表。
isomorphic-git, lightning-fs
D2.4: 基础公众号排版
1. 创建一个 markdownToWechat 转换函数。<br>2. 实现基本的样式映射（标题、段落、列表）和代码块的 Shiki 高亮。<br>3. 实现“一键复制”功能。
unified, Shiki, Clipboard API

阶段三：MVP 测试与发布 (Phase 3: MVP Testing & Launch)
目标: 通过内部测试和少量用户测试，打磨产品质量，完成首次线上部署。
任务 (Task)
详细步骤 (Detailed Steps)
关键工具 / 技术
D3.1: Alpha 测试 (个人)
1. 参照 PRD，逐一测试所有 MVP 功能，包括边界情况（如超长文本、断网）。<br>2. 进行主流浏览器兼容性测试 (Chrome, Firefox, Safari)。
浏览器开发者工具
D3.2: Beta 测试 (种子用户)
1. 使用 Vercel 将项目部署到线上，生成 URL。<br>2. 邀请 5-10 位朋友或种子用户进行测试。<br>3. 通过 GitHub Issues 或问卷收集反馈。
Vercel, GitHub Issues
D3.3: Bug 修复与优化
1. 根据测试反馈，集中修复 Bug。<br>2. 使用 Lighthouse 分析并优化性能。<br>3. 集成 Sentry 进行线上错误监控。
Lighthouse, Sentry
D3.4: MVP 正式发布
1. 购买域名并绑定到 Vercel。<br>2. 撰写产品介绍 README.md 和落地页。<br>3. 在 V2EX, Product Hunt 等社区进行首次亮相。
Vercel, Namecheap

阶段四：功能迭代与完善 (Phase 4: Feature Iteration)
目标: 在 MVP 基础上，根据用户反馈和产品路线图，逐步增加高阶功能，构建产品护城河。
任务 (Task)
详细步骤 (Detailed Steps)
关键技术
D4.1: 远程 Git 仓库同步
1. 使用 Next.js API Routes 实现 GitHub OAuth 认证。<br>2. 实现 git push, pull, clone 功能（可能需要 CORS 代理）。
Next.js API Routes, GitHub API
D4.2: 增强排版引擎
1. 增加对知乎、小红书等平台的主题模板。<br>2. 实现图片自动压缩和上传到图床（如 Cloudinary）。
Cloudinary API
D4.3: 协作功能 (高阶)
1. 集成 Yjs 和 y-codemirror。<br>2. 搭建 y-websocket 中继服务器。
Yjs, y-websocket
D4.4: AI 智能审核 (高阶)
1. 研究并集成轻量级本地文本审核 JS 库或云服务 API。<br>2. 在发布前提供风险提示。
TensorFlow.js, 云服务 API

阶段五：成品打磨与持续运营 (Phase 5: Polishing & Operation)
目标: 将产品打磨成一个精致、稳定、可靠的成熟应用，并建立长期运营机制。
任务 (Task)
详细步骤 (Detailed Steps)
关键工具 / 技术
D5.1: 全面国际化 (i18n)
1. 集成 next-i18next 或类似库。<br>2. 将所有 UI 文本抽离到语言文件中。
next-i18next
D5.2: 无障碍 (a11y) 优化
1. 使用 axe DevTools 审计并修复无障碍性问题。<br>2. 确保所有交互元素都可键盘访问，并有正确的 aria 属性。
axe DevTools
D5.3: 建立文档与社区
1. 使用 VitePress 或 Nextra 撰写详细的用户帮助文档。<br>2. 创建 GitHub Discussions 或 Discord 频道作为用户社区。
VitePress, Discord
D5.4: 探索商业模式
1. 设计 Pro 版本功能（如高级协作、私有仓库、AI 用量等）。<br>2. 集成 Stripe 或 Lemon Squeezy 支付网关。
Stripe, Lemon Squeezy

结论
这份“终极开发蓝图”是你构建“写乎”项目的罗盘和地图。它将复杂的技术选型和开发流程标准化、阶段化，让你能将精力完全集中在当前阶段最重要的事情上。祝你开发顺利！