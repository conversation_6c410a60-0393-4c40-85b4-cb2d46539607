# 写乎 (DraftFlux) - 开发说明文档

## 📋 目录
- [项目概述](#项目概述)
- [技术架构](#技术架构)
- [开发计划](#开发计划)
- [快速开始](#快速开始)
- [项目结构](#项目结构)
- [开发规范](#开发规范)
- [部署说明](#部署说明)
- [相关资源](#相关资源)

## 📋 项目概述

**版本**: 2.0 (最终版)
**状态**: 行动纲领 (Master Plan)
**目标**: 构建一个本地优先的 Markdown 编辑器，支持 Git 版本控制和多平台内容发布

本文档是"写乎"项目的唯一真相来源 (Single Source of Truth)，整合了项目的最终技术选型与从概念到成熟产品的完整分阶段开发计划，为个人开发者提供清晰、全面、可执行的终极指南。

### 主要功能
- [x] 本地优先的 Markdown 编辑器
- [x] Git 版本控制集成
- [x] 多平台内容发布（微信公众号、知乎、小红书等）
- [ ] 实时协作功能
- [ ] AI 智能审核
- [ ] 远程仓库同步

### 项目特色
- **本地优先**: 使用 IndexedDB 实现离线编辑
- **Git 集成**: 浏览器中的完整 Git 工作流
- **多平台发布**: 一键转换为各平台格式
- **高性能**: 基于 Next.js 的现代 Web 应用

## 🏗️ 技术架构

### 技术栈总览

| 类别 | 主要技术/平台 |
|------|---------------|
| **核心框架** | Next.js (App Router), React, TypeScript |
| **UI 与样式** | Tailwind CSS, shadcn/ui |
| **核心功能库** | CodeMirror 6, Dexie.js, isomorphic-git, unified (Remark/Rehype), Shiki |
| **后端与数据** | Next.js API Routes, Vercel Postgres / Supabase |
| **开发与运维** | GitHub, Vercel, GitHub Actions |
| **分析与监控** | Vercel Analytics, Sentry |

### 核心技术选型详解

#### 🚀 Next.js - 全栈框架
- **选择理由**: 集成 SSR、SSG 和后端 API 的全栈解决方案，为 SEO 优化提供基础
- **实施策略**:
  - 使用 App Router 进行文件系统路由
  - 公共页面采用 SSG 获得最佳性能
  - 编辑器核心使用客户端渲染
  - 用户分享页面采用 ISR 或 SSR

#### 🎨 UI 框架 - Tailwind CSS + shadcn/ui
- **选择理由**: 提供极致开发速度和完全设计自由度
- **实施方式**:
  ```bash
  pnpm dlx shadcn-ui@latest init
  ```

#### 🔧 核心功能库
- **CodeMirror 6**: 高性能、可定制的 Markdown 编辑器
- **Dexie.js**: 优雅封装 IndexedDB，实现本地优先数据存储
- **isomorphic-git**: 浏览器中的 Git 核心能力，产品差异化关键
- **unified (Remark/Rehype)**: Markdown 到 HTML 精准转换
- **Shiki**: VS Code 风格的 SEO 友好代码高亮

#### 💾 后端与数据
- **Next.js API Routes**: 处理 GitHub OAuth、Git 远程操作 CORS 代理、支付处理
- **Vercel Postgres / Supabase**: 存储用户账户、设置、订阅状态

#### 🔄 开发与运维
- **GitHub + Vercel**: 现代 Web 开发黄金搭档
- **CI/CD 流程**:
  - 每次 `git push` 自动触发构建部署
  - 每个 Pull Request 自动生成预览环境

## 💻 环境要求

### 系统要求
- 操作系统: macOS, Windows, Linux
- 内存: 8GB 以上推荐
- 存储空间: 2GB 可用空间

### 软件依赖
```bash
Node.js >= 18.0.0
pnpm >= 8.0.0 (推荐)
Git >= 2.30.0
```

## 🚀 快速开始

### 1. 项目初始化
```bash
# 创建项目
npx create-next-app@latest draftflux --typescript --tailwind --eslint

# 进入项目目录
cd draftflux
```

### 2. 安装依赖
```bash
# 安装基础依赖
pnpm install

# 初始化 shadcn/ui
pnpm dlx shadcn-ui@latest init
```

### 3. 安装核心功能库
```bash
# 编辑器相关
pnpm add @codemirror/state @codemirror/view @codemirror/lang-markdown
pnpm add @codemirror/theme-one-dark @codemirror/commands

# 本地存储
pnpm add dexie

# Git 功能
pnpm add isomorphic-git lightning-fs

# Markdown 处理
pnpm add unified remark-parse remark-rehype rehype-stringify
pnpm add remark-gfm rehype-highlight shiki

# 其他工具
pnpm add clsx tailwind-merge lucide-react
```

### 4. 环境配置
```bash
# 复制环境配置文件
cp .env.example .env.local

# 编辑配置文件
vim .env.local
```

### 5. 启动开发服务器
```bash
pnpm dev
```

### 6. 访问应用
- 开发地址: http://localhost:3000
- 编辑器: http://localhost:3000/editor
- API 文档: http://localhost:3000/api

## 📁 项目结构

```
draftflux/
├── app/                    # Next.js App Router
│   ├── (auth)/            # 认证相关页面
│   ├── editor/            # 编辑器页面
│   ├── api/               # API 路由
│   ├── globals.css        # 全局样式
│   ├── layout.tsx         # 根布局
│   └── page.tsx           # 首页
├── components/            # React 组件
│   ├── ui/               # shadcn/ui 组件
│   ├── editor/           # 编辑器组件
│   ├── git/              # Git 相关组件
│   └── layout/           # 布局组件
├── lib/                   # 工具库
│   ├── db/               # 数据库相关
│   ├── git/              # Git 操作
│   ├── markdown/         # Markdown 处理
│   └── utils.ts          # 通用工具
├── hooks/                 # React Hooks
├── types/                 # TypeScript 类型定义
├── public/               # 静态资源
├── docs/                 # 项目文档
├── .env.example          # 环境变量示例
├── next.config.js        # Next.js 配置
├── tailwind.config.js    # Tailwind 配置
├── tsconfig.json         # TypeScript 配置
└── package.json          # 项目配置
```

## 📝 开发规范

### 代码规范
- 使用 TypeScript 进行类型检查
- 使用 ESLint + Prettier 进行代码格式化
- 遵循 React Hooks 最佳实践
- 组件使用 PascalCase 命名
- 文件使用 kebab-case 命名

### Git 提交规范
```bash
# 提交格式
<type>(<scope>): <subject>

# 类型说明
feat: 新功能
fix: 修复 bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(editor): 添加 Markdown 实时预览功能
fix(git): 修复提交历史显示错误
docs(readme): 更新安装说明
```

### 分支管理
- `main`: 主分支，用于生产环境
- `develop`: 开发分支，用于集成功能
- `feature/*`: 功能分支
- `hotfix/*`: 紧急修复分支

### 代码审查
- 所有代码变更需要通过 Pull Request
- 确保 TypeScript 类型检查通过
- 确保 ESLint 检查通过
- 添加必要的测试用例

## 📅 开发计划

### 阶段一：概念与设计 (Phase 1)
**目标**: 将产品构想转化为具体设计蓝图和技术方案

#### 任务清单
- [ ] **D1.1 界面线框图**
  - 使用 Excalidraw/Figma 绘制核心界面低保真线框图
  - 明确主编辑界面、历史记录面板、设置页面布局

- [ ] **D1.2 视觉风格指南**
  - 定义主色、辅助色、背景色、文本颜色（亮/暗模式）
  - 选定字体：UI 字体 (Inter)、代码字体 (Source Code Pro)
  - 确定间距、圆角等设计规范

- [ ] **D1.3 项目初始化**
  - 完成 Next.js 项目创建和基础配置
  - 集成 TypeScript, Tailwind CSS, ESLint, Prettier
  - 初始化 shadcn/ui 组件库

### 阶段二：MVP 开发 (Phase 2)
**目标**: 构建产品核心功能，开发最小化可行产品

#### 任务清单
- [ ] **D2.1 核心编辑器**
  - 实现双栏布局：CodeMirror 编辑器 + 实时预览
  - 编辑器与 React state 双向绑定（带防抖）
  - 使用 unified 实现基础 Markdown 预览

- [ ] **D2.2 本地存储**
  - 集成 Dexie.js，设计 documents 数据表
  - 实现自动读取和保存逻辑

- [ ] **D2.3 基础 Git 工作流**
  - 集成 isomorphic-git 和 lightning-fs
  - 实现"保存版本"功能（git commit）
  - 实现"历史记录"功能（git log）

- [ ] **D2.4 基础公众号排版**
  - 创建 markdownToWechat 转换函数
  - 实现基本样式映射和 Shiki 代码高亮
  - 实现"一键复制"功能

### 阶段三：MVP 测试与发布 (Phase 3)
**目标**: 通过测试打磨产品质量，完成首次线上部署

#### 任务清单
- [ ] **D3.1 Alpha 测试**
  - 逐一测试所有 MVP 功能，包括边界情况
  - 主流浏览器兼容性测试

- [ ] **D3.2 Beta 测试**
  - Vercel 部署到线上
  - 邀请 5-10 位种子用户测试
  - 通过 GitHub Issues 收集反馈

- [ ] **D3.3 Bug 修复与优化**
  - 根据反馈修复 Bug
  - Lighthouse 性能优化
  - 集成 Sentry 错误监控

- [ ] **D3.4 MVP 正式发布**
  - 购买域名并绑定 Vercel
  - 撰写产品介绍和落地页
  - 在 V2EX、Product Hunt 等社区发布

### 阶段四：功能迭代与完善 (Phase 4)
**目标**: 根据用户反馈增加高阶功能，构建产品护城河

#### 任务清单
- [ ] **D4.1 远程 Git 仓库同步**
  - Next.js API Routes 实现 GitHub OAuth
  - 实现 git push, pull, clone 功能

- [ ] **D4.2 增强排版引擎**
  - 增加知乎、小红书等平台主题模板
  - 图片自动压缩和上传到图床

- [ ] **D4.3 协作功能**
  - 集成 Yjs 和 y-codemirror
  - 搭建 y-websocket 中继服务器

- [ ] **D4.4 AI 智能审核**
  - 集成文本审核 JS 库或云服务 API
  - 发布前风险提示

### 阶段五：成品打磨与持续运营 (Phase 5)
**目标**: 打磨成精致、稳定、可靠的成熟应用

#### 任务清单
- [ ] **D5.1 全面国际化**
  - 集成 next-i18next
  - UI 文本抽离到语言文件

- [ ] **D5.2 无障碍优化**
  - axe DevTools 审计修复
  - 键盘访问和 aria 属性完善

- [ ] **D5.3 建立文档与社区**
  - VitePress/Nextra 撰写帮助文档
  - GitHub Discussions/Discord 用户社区

- [ ] **D5.4 探索商业模式**
  - 设计 Pro 版本功能
  - 集成 Stripe/Lemon Squeezy 支付

## 🧪 测试

### 运行测试
```bash
# 运行所有测试
pnpm test

# 运行特定测试
pnpm test -- --grep "编辑器"

# 生成测试覆盖率报告
pnpm run test:coverage

# E2E 测试
pnpm run test:e2e
```

### 测试规范
- 单元测试覆盖率 > 80%
- 集成测试覆盖核心功能（编辑器、Git、存储）
- E2E 测试覆盖关键用户流程（创建文档、保存版本、发布）

## 🚀 部署说明

### Vercel 部署（推荐）
```bash
# 1. 安装 Vercel CLI
npm i -g vercel

# 2. 登录 Vercel
vercel login

# 3. 部署项目
vercel

# 4. 生产环境部署
vercel --prod
```

### 环境变量配置
| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| NEXTAUTH_SECRET | NextAuth 密钥 | - | 是 |
| GITHUB_CLIENT_ID | GitHub OAuth ID | - | 否 |
| GITHUB_CLIENT_SECRET | GitHub OAuth Secret | - | 否 |
| DATABASE_URL | 数据库连接 | - | 否 |
| SENTRY_DSN | Sentry 错误监控 | - | 否 |

### Docker 部署
```bash
# 构建镜像
docker build -t draftflux .

# 运行容器
docker run -p 3000:3000 draftflux
```

## ❓ 常见问题

### Q: 如何解决 Git 操作在浏览器中失败？
A: 确保正确配置了 CORS 和文件系统权限：
```bash
# 检查 lightning-fs 配置
# 确保在 HTTPS 环境下运行
```

### Q: CodeMirror 编辑器性能问题？
A: 优化大文档处理：
```javascript
// 启用虚拟滚动
import { EditorView } from '@codemirror/view'
const view = new EditorView({
  extensions: [
    EditorView.theme({
      '&': { maxHeight: '400px' },
      '.cm-scroller': { overflow: 'auto' }
    })
  ]
})
```

### Q: 如何调试 IndexedDB 存储问题？
A: 使用浏览器开发者工具：
```bash
# Chrome DevTools -> Application -> Storage -> IndexedDB
# 检查 draftflux 数据库和 documents 表
```

## 📚 相关资源

### 官方文档
- [Next.js 文档](https://nextjs.org/docs)
- [Tailwind CSS 文档](https://tailwindcss.com/docs)
- [shadcn/ui 组件库](https://ui.shadcn.com/)
- [CodeMirror 6 文档](https://codemirror.net/docs/)
- [Dexie.js 文档](https://dexie.org/)

### 学习资源
- [React 官方教程](https://react.dev/learn)
- [TypeScript 手册](https://www.typescriptlang.org/docs/)
- [Git 学习指南](https://git-scm.com/book)
- [Markdown 语法指南](https://www.markdownguide.org/)

### 社区资源
- [Next.js GitHub](https://github.com/vercel/next.js)
- [React 中文社区](https://react.docschina.org/)
- [TypeScript 中文网](https://www.tslang.cn/)

## 🎯 总结

这份开发说明文档将复杂的技术选型和开发流程标准化、阶段化，让开发者能将精力完全集中在当前阶段最重要的事情上。按照这个计划，您可以系统性地构建出一个功能完整、技术先进的 Markdown 编辑器产品。

### 下一步行动
1. 完成阶段一的设计工作
2. 搭建基础项目架构
3. 实现核心编辑器功能
4. 逐步迭代和完善功能

祝您开发顺利！🚀

---

**最后更新**: 2024年12月
**文档版本**: v2.0
